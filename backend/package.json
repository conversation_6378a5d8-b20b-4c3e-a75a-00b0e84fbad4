{"dependencies": {"@fastify/cors": "^9.0.0", "better-sqlite3": "^12.2.0", "fastify": "^4.27.0", "knex": "^3.1.0"}, "name": "backend", "version": "1.0.0", "main": "index.js", "devDependencies": {"nodemon": "^3.1.9"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "migrate": "knex migrate:latest", "seed": "knex seed:run", "start": "node index.js", "dev": "PORT=3001 nodemon index.js"}, "author": "", "license": "ISC", "description": "", "type": "module", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}