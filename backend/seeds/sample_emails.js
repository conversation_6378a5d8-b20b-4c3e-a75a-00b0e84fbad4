/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
export const seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('emails').del();
  
  // Inserts seed entries
  await knex('emails').insert([
    {
      to: '<EMAIL>',
      cc: '<EMAIL>',
      bcc: '',
      subject: 'Welcome to the team!',
      body: '<PERSON> <PERSON>,\n\nWelcome to our team! We are excited to have you on board. Please let me know if you have any questions.\n\nBest regards,\nHR Team'
    },
    {
      to: '<EMAIL>',
      cc: '',
      bcc: '',
      subject: 'Project Update - Q4 Goals',
      body: '<PERSON>,\n\nI wanted to give you an update on our Q4 goals. We are making good progress on all fronts.\n\nPlease review the attached document and let me know your thoughts.\n\nThanks,\nProject Manager'
    },
    {
      to: '<EMAIL>',
      cc: '<EMAIL>',
      bcc: '',
      subject: 'All Hands Meeting - Friday 2PM',
      body: 'Dear Team,\n\nWe will be having an all hands meeting this Friday at 2PM in the main conference room.\n\nAgenda:\n- Q4 Review\n- New Product Launch\n- Team Updates\n\nSee you there!\nManagement'
    },
    {
      to: '<EMAIL>',
      cc: '',
      bcc: '',
      subject: 'Bug Report - Login Issue',
      body: 'Hello Support Team,\n\nI am experiencing issues with the login functionality. When I try to log in, I get an error message.\n\nSteps to reproduce:\n1. Go to login page\n2. Enter credentials\n3. Click login\n4. Error appears\n\nPlease help.\nUser'
    },
    {
      to: '<EMAIL>',
      cc: '<EMAIL>',
      bcc: '',
      subject: 'New Campaign Launch',
      body: 'Hi Marketing Team,\n\nWe are ready to launch our new campaign next week. All assets are prepared and the landing page is live.\n\nPlease coordinate with the sales team for the launch.\n\nBest,\nCampaign Manager'
    }
  ]);
};
