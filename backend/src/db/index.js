import knex from 'knex';

const db = knex({
  client: 'better-sqlite3',
  connection: {
    filename: './dev.sqlite3',
  },
  useNullAsDefault: true
});

class DB {
  static async addLead(data) {
    return db('leads').insert(data);
  }

  static async getAllEmails() {
    return db('emails').select('*').orderBy('created_at', 'desc');
  }

  static async getEmailById(id) {
    return db('emails').where('id', id).first();
  }

  static async createEmail(data) {
    return db('emails').insert(data).returning('*');
  }

  static async searchEmails(searchTerm) {
    return db('emails')
      .where('to', 'like', `%${searchTerm}%`)
      .orWhere('cc', 'like', `%${searchTerm}%`)
      .orWhere('bcc', 'like', `%${searchTerm}%`)
      .orWhere('subject', 'like', `%${searchTerm}%`)
      .orWhere('body', 'like', `%${searchTerm}%`)
      .orderBy('created_at', 'desc');
  }
}

export default DB;
