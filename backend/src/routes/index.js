import DB from '../db/index.js';

export default async function routes(fastify, options) {
  // Enable CORS for frontend
  await fastify.register(import('@fastify/cors'), {
    origin: ['http://localhost:3000']
  });

  fastify.get('/ping', async (request, reply) => {
    return 'pong\n';
  });

  // Get all emails
  fastify.get('/emails', async (request, reply) => {
    try {
      const emails = await DB.getAllEmails();
      return emails;
    } catch (error) {
      reply.code(500).send({ error: 'Failed to fetch emails' });
    }
  });

  // Get email by ID
  fastify.get('/emails/:id', async (request, reply) => {
    try {
      const { id } = request.params;
      const email = await DB.getEmailById(id);
      if (!email) {
        reply.code(404).send({ error: 'Email not found' });
        return;
      }
      return email;
    } catch (error) {
      reply.code(500).send({ error: 'Failed to fetch email' });
    }
  });

  // Search emails
  fastify.get('/emails/search/:term', async (request, reply) => {
    try {
      const { term } = request.params;
      const emails = await DB.searchEmails(term);
      return emails;
    } catch (error) {
      reply.code(500).send({ error: 'Failed to search emails' });
    }
  });

  // Create new email
  fastify.post('/emails', async (request, reply) => {
    try {
      const emailData = request.body;
      const result = await DB.createEmail(emailData);
      return result;
    } catch (error) {
      reply.code(500).send({ error: 'Failed to create email' });
    }
  });
}
