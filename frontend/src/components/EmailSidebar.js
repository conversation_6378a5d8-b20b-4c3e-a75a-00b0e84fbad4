import React, { useState, useEffect } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  Divider,
  Paper,
  TextField,
  InputAdornment
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

const EmailSidebar = ({ onEmailSelect, selectedEmailId }) => {
  const [emails, setEmails] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  // Fetch emails from backend
  useEffect(() => {
    fetchEmails();
  }, []);

  const fetchEmails = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3001/emails');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setEmails(data);
    } catch (error) {
      console.error('Error fetching emails:', error);
      setEmails([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.trim()) {
        searchEmails(searchTerm);
      } else {
        fetchEmails();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const searchEmails = async (term) => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:3001/emails/search/${encodeURIComponent(term)}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setEmails(data);
    } catch (error) {
      console.error('Error searching emails:', error);
      setEmails([]);
    } finally {
      setLoading(false);
    }
  };

  const handleEmailClick = (email) => {
    onEmailSelect(email);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateText = (text, maxLength = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Paper
      elevation={1}
      sx={{
        width: 350,
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 0,
        borderRight: '1px solid #e0e0e0'
      }}
    >
      {/* Search Bar */}
      <Box sx={{ p: 2 }}>
        <TextField
          fullWidth
          placeholder="Search emails..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          size="small"
        />
      </Box>

      <Divider />

      {/* Email List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {loading ? (
          <Box sx={{ p: 2 }}>
            <Typography>Loading emails...</Typography>
          </Box>
        ) : emails.length === 0 ? (
          <Box sx={{ p: 2 }}>
            <Typography color="text.secondary">
              {searchTerm ? 'No emails found' : 'No emails available'}
            </Typography>
          </Box>
        ) : (
          <List disablePadding>
            {emails.map((email) => (
              <React.Fragment key={email.id}>
                <ListItem disablePadding>
                  <ListItemButton
                    selected={selectedEmailId === email.id}
                    onClick={() => handleEmailClick(email)}
                    sx={{
                      py: 2,
                      px: 2,
                      '&.Mui-selected': {
                        backgroundColor: 'primary.light',
                        '&:hover': {
                          backgroundColor: 'primary.light',
                        },
                      },
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box>
                          <Typography 
                            variant="subtitle2" 
                            fontWeight="bold"
                            noWrap
                          >
                            {email.to}
                          </Typography>
                          <Typography 
                            variant="body2" 
                            fontWeight="medium"
                            noWrap
                            sx={{ mt: 0.5 }}
                          >
                            {email.subject}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography 
                            variant="body2" 
                            color="text.secondary"
                            sx={{ 
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                            }}
                          >
                            {truncateText(email.body, 80)}
                          </Typography>
                          <Typography 
                            variant="caption" 
                            color="text.secondary"
                            sx={{ mt: 0.5, display: 'block' }}
                          >
                            {formatDate(email.created_at)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItemButton>
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>
    </Paper>
  );
};

export default EmailSidebar;
